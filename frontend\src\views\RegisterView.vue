<template>
  <main
    class="min-h-screen bg-gradient-to-br from-[#EDF6FF] via-[#F5F5F5] to-[#FFF1DB] flex items-center justify-center p-4 relative overflow-hidden">
    <!-- Animated Background -->
    <div class="absolute inset-0">
      <!-- Primary Gradient Overlay -->
      <div class="absolute inset-0 bg-gradient-to-br from-[#1E4E79]/10 via-transparent to-[#C1843E]/10 animate-pulse"></div>
      <!-- Floating Animated Elements -->
      <div class="absolute top-20 left-20 w-32 h-32 bg-[#1E4E79]/15 rounded-full blur-xl animate-[float_6s_ease-in-out_infinite]"></div>
      <div class="absolute bottom-20 right-20 w-40 h-40 bg-[#C1843E]/15 rounded-full blur-xl animate-[float_8s_ease-in-out_infinite_reverse]"></div>
    </div>

    <!-- Register Card -->
    <div class="relative w-full max-w-md animate-[slideUp_0.8s_ease-out]">
      <!-- Main Card -->
      <div class="bg-[#FFFFFF] rounded-2xl shadow-lg border border-[#CCCCCC]/30 overflow-hidden backdrop-blur-sm hover:shadow-xl transition-all duration-500">
        <!-- Header -->
        <div class="px-8 pt-8 pb-6 text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-[#C1843E] to-[#704A1F] rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg animate-[bounce_1s_ease-out_0.5s] hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
            </svg>
          </div>
          <h1 class="text-2xl font-bold text-[#111111] mb-2 animate-[fadeIn_1s_ease-out_0.3s_both]">Join Mthunzi</h1>
          <p class="text-[#4B4B4B] text-sm animate-[fadeIn_1s_ease-out_0.5s_both]">Create your <span class="font-semibold bg-gradient-to-r from-[#1E4E79] to-[#C1843E] bg-clip-text text-transparent">Pod</span> account</p>
        </div>

        <!-- Form -->
        <div class="px-8 pb-8">
          <form @submit.prevent="handleRegister" class="space-y-6">
            <!-- Business Name Field -->
            <div class="space-y-2 animate-[slideInLeft_0.6s_ease-out_0.7s_both]">
              <label for="businessName" class="block text-sm font-medium text-[#111111]">
                Business Name
              </label>
              <div class="relative group">
                <input
                  id="businessName"
                  type="text"
                  v-model="businessName"
                  placeholder="Enter your business name"
                  class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none hover:bg-[#FFFFFF] group-hover:shadow-md"
                  required
                />
                <div class="absolute inset-y-0 right-0 flex items-center pr-4 transition-colors duration-300">
                  <svg class="w-5 h-5 text-[#4B4B4B] group-focus-within:text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Owner Name Field -->
            <div class="space-y-2 animate-[slideInRight_0.6s_ease-out_0.9s_both]">
              <label for="ownerName" class="block text-sm font-medium text-[#111111]">
                Owner Name
              </label>
              <div class="relative group">
                <input
                  id="ownerName"
                  type="text"
                  v-model="ownerName"
                  placeholder="Enter your full name"
                  class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none hover:bg-[#FFFFFF] group-hover:shadow-md"
                  required
                />
                <div class="absolute inset-y-0 right-0 flex items-center pr-4 transition-colors duration-300">
                  <svg class="w-5 h-5 text-[#4B4B4B] group-focus-within:text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Email Field -->
            <div class="space-y-2 animate-[slideInLeft_0.6s_ease-out_1.1s_both]">
              <label for="email" class="block text-sm font-medium text-[#111111]">
                Email Address
              </label>
              <div class="relative group">
                <input
                  id="email"
                  type="email"
                  v-model="email"
                  placeholder="Enter your email"
                  class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none hover:bg-[#FFFFFF] group-hover:shadow-md"
                  required
                />
                <div class="absolute inset-y-0 right-0 flex items-center pr-4 transition-colors duration-300">
                  <svg class="w-5 h-5 text-[#4B4B4B] group-focus-within:text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Password Field -->
            <div class="space-y-2 animate-[slideInRight_0.6s_ease-out_1.3s_both]">
              <label for="password" class="block text-sm font-medium text-[#111111]">
                Password
              </label>
              <div class="relative group">
                <input
                  id="password"
                  :type="showPassword ? 'text' : 'password'"
                  v-model="password"
                  placeholder="Create a password"
                  class="w-full px-4 py-3 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] placeholder-[#4B4B4B] focus:bg-[#FFFFFF] focus:ring-2 focus:ring-[#1E4E79]/20 focus:border-[#1E4E79] transition-all duration-300 outline-none pr-12 hover:bg-[#FFFFFF] group-hover:shadow-md"
                  required
                />
                <button
                  type="button"
                  @click="showPassword = !showPassword"
                  class="absolute inset-y-0 right-0 flex items-center pr-4 text-[#4B4B4B] hover:text-[#1E4E79] transition-all duration-300 hover:scale-110"
                >
                  <svg v-if="!showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                  </svg>
                  <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Terms & Conditions -->
            <div class="animate-[fadeIn_0.6s_ease-out_1.5s_both]">
              <label class="flex items-start group cursor-pointer">
                <input
                  type="checkbox"
                  v-model="acceptTerms"
                  class="w-4 h-4 text-[#1E4E79] bg-[#F5F5F5] border-[#CCCCCC] rounded focus:ring-[#1E4E79] focus:ring-2 transition-all duration-300 mt-1"
                  required
                />
                <span class="ml-3 text-sm text-[#4B4B4B] group-hover:text-[#111111] transition-colors duration-300">
                  I agree to the <a href="#" class="text-[#1E4E79] hover:text-[#C1843E] font-medium transition-colors duration-300 hover:underline">Terms & Conditions</a> and <a href="#" class="text-[#1E4E79] hover:text-[#C1843E] font-medium transition-colors duration-300 hover:underline">Privacy Policy</a>
                </span>
              </label>
            </div>

            <!-- Register Button -->
            <button
              type="submit"
              :disabled="isLoading"
              class="w-full bg-gradient-to-r from-[#C1843E] to-[#704A1F] hover:from-[#704A1F] hover:to-[#C1843E] disabled:from-[#4B4B4B] disabled:to-[#4B4B4B] text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg active:scale-[0.98] disabled:transform-none disabled:cursor-not-allowed flex items-center justify-center animate-[slideUp_0.6s_ease-out_1.7s_both] group"
            >
              <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="group-hover:tracking-wide transition-all duration-300">{{ isLoading ? 'Creating account...' : 'Create Pod Account' }}</span>
            </button>
          </form>

          <!-- Divider -->
          <div class="relative my-6 animate-[fadeIn_0.6s_ease-out_1.9s_both]">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-[#CCCCCC]/50"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-4 bg-[#FFFFFF] text-[#4B4B4B]">or</span>
            </div>
          </div>

          <!-- Login Link -->
          <div class="text-center animate-[fadeIn_0.6s_ease-out_2.1s_both]">
            <p class="text-sm text-[#4B4B4B]">
              Already have an account?
              <router-link to="/" class="font-semibold text-[#1E4E79] hover:text-[#C1843E] transition-all duration-300 hover:underline">
                Sign in
              </router-link>
            </p>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="mt-8 text-center animate-[fadeIn_0.8s_ease-out_2.3s_both]">
        <p class="text-xs text-[#4B4B4B]">
          A <span class="font-semibold bg-gradient-to-r from-[#1E4E79] to-[#C1843E] bg-clip-text text-transparent">NorthForm</span> Production
        </p>
      </div>
    </div>
  </main>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// Form data
const businessName = ref('')
const ownerName = ref('')
const email = ref('')
const password = ref('')
const acceptTerms = ref(false)
const showPassword = ref(false)
const isLoading = ref(false)

// Handle registration
const handleRegister = async () => {
  isLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Handle successful registration
    console.log('Registration attempt:', {
      businessName: businessName.value,
      ownerName: ownerName.value,
      email: email.value,
      password: password.value,
      acceptTerms: acceptTerms.value
    })
    
    // Redirect to pod dashboard
    router.push('/pod')
    
  } catch (error) {
    console.error('Registration failed:', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}
</style>
